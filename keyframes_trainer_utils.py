import math
import numpy as np
import torch
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
from keyframes_dataset import KeyframesParser


def knn(points: torch.Tensor, k: int) -> torch.Tensor:
    """
    Find k nearest neighbors for each point.
    
    Args:
        points: Point coordinates [N, 3]
        k: Number of nearest neighbors
    
    Returns:
        distances: Distances to k nearest neighbors [N, k]
    """
    # Compute pairwise distances
    dists = torch.cdist(points, points)  # [N, N]
    
    # Find k nearest neighbors (including self)
    knn_dists, _ = torch.topk(dists, k, dim=1, largest=False)
    
    return knn_dists


def rgb_to_sh(rgb: torch.Tensor) -> torch.Tensor:
    """
    Convert RGB colors to spherical harmonics coefficients.

    Args:
        rgb: RGB colors [N, 3]

    Returns:
        sh: SH coefficients [N, 1, 3]
    """
    # Ensure input is [N, 3]
    if rgb.dim() == 3 and rgb.shape[1] == 1:
        rgb = rgb.squeeze(1)  # [N, 1, 3] -> [N, 3]

    # Convert RGB to SH (0th order)
    C0 = 0.28209479177387814
    sh = (rgb - 0.5) / C0

    # Add the SH dimension: [N, 3] -> [N, 1, 3]
    sh = sh.unsqueeze(1)

    return sh


def create_splats_with_optimizers_keyframes(
    parser: KeyframesParser,
    init_opacity: float = 0.1,
    init_scale: float = 1.0,
    means_lr: float = 1.6e-4,
    scales_lr: float = 5e-3,
    opacities_lr: float = 5e-2,
    quats_lr: float = 1e-3,
    sh0_lr: float = 2.5e-3,
    shN_lr: float = 2.5e-3 / 20,
    scene_scale: float = 1.0,
    sh_degree: int = 3,
    sparse_grad: bool = False,
    visible_adam: bool = False,
    batch_size: int = 1,
    feature_dim: Optional[int] = None,
    device: str = "cuda",
    world_rank: int = 0,
    world_size: int = 1,
    confidence_threshold: float = 0.0,
) -> Tuple[torch.nn.ParameterDict, Dict[str, torch.optim.Optimizer]]:
    """
    Create 3D Gaussian splats and optimizers from keyframes data.
    
    Args:
        parser: KeyframesParser containing point cloud and RGB data
        init_opacity: Initial opacity value
        init_scale: Scale factor for initial Gaussian sizes
        means_lr: Learning rate for 3D positions
        scales_lr: Learning rate for scales
        opacities_lr: Learning rate for opacities
        quats_lr: Learning rate for rotations
        sh0_lr: Learning rate for SH band 0
        shN_lr: Learning rate for higher SH bands
        scene_scale: Scene scale factor
        sh_degree: Maximum SH degree
        sparse_grad: Whether to use sparse gradients
        visible_adam: Whether to use visible Adam optimizer
        batch_size: Batch size for training
        feature_dim: Feature dimension for appearance modeling
        device: Device to create tensors on
        world_rank: Current process rank
        world_size: Total number of processes
        confidence_threshold: Minimum confidence for points
    
    Returns:
        splats: Parameter dictionary containing Gaussian parameters
        optimizers: Dictionary of optimizers for each parameter group
    """
    # Get points and colors from parser
    points = torch.from_numpy(parser.points).float()
    rgbs = torch.from_numpy(parser.points_rgb / 255.0).float()
    
    print(f"Initializing 3DGS with {len(points)} points")
    print(f"Points range: X[{points[:, 0].min():.3f}, {points[:, 0].max():.3f}], "
          f"Y[{points[:, 1].min():.3f}, {points[:, 1].max():.3f}], "
          f"Z[{points[:, 2].min():.3f}, {points[:, 2].max():.3f}]")
    
    # Filter points by confidence if available
    if hasattr(parser.dataset, 'data_list') and len(parser.dataset.data_list) > 0:
        confidences = parser.dataset.data_list[0]['confidences']
        if confidence_threshold > 0:
            valid_mask = confidences.squeeze() >= confidence_threshold
            points = points[valid_mask]
            rgbs = rgbs[valid_mask]
            print(f"After confidence filtering: {len(points)} points")
    
    # Initialize the GS size to be the average dist of the 3 nearest neighbors
    if len(points) > 3:
        dist2_avg = (knn(points, 4)[:, 1:] ** 2).mean(dim=-1)  # [N,]
        dist_avg = torch.sqrt(dist2_avg)
    else:
        # Fallback for very few points
        dist_avg = torch.full((len(points),), 0.01)
    
    scales = torch.log(dist_avg * init_scale).unsqueeze(-1).repeat(1, 3)  # [N, 3]
    
    # Distribute the GSs to different ranks (also works for single rank)
    points = points[world_rank::world_size]
    rgbs = rgbs[world_rank::world_size]
    scales = scales[world_rank::world_size]
    
    N = points.shape[0]
    print(f"Process {world_rank}: {N} points after distribution")
    
    if N == 0:
        raise ValueError(f"No points assigned to rank {world_rank}")
    
    # Initialize rotations (quaternions)
    quats = torch.rand((N, 4))  # [N, 4]
    quats = F.normalize(quats, dim=-1)  # Normalize quaternions
    
    # Initialize opacities
    opacities = torch.logit(torch.full((N,), init_opacity))  # [N,]
    
    params = [
        # name, value, lr
        ("means", torch.nn.Parameter(points), means_lr * scene_scale),
        ("scales", torch.nn.Parameter(scales), scales_lr),
        ("quats", torch.nn.Parameter(quats), quats_lr),
        ("opacities", torch.nn.Parameter(opacities), opacities_lr),
    ]
    
    if feature_dim is None:
        # Color is SH coefficients
        colors = torch.zeros((N, (sh_degree + 1) ** 2, 3))  # [N, K, 3]
        sh_colors = rgb_to_sh(rgbs)  # [N, 1, 3]
        colors[:, :1, :] = sh_colors  # Assign the [N, 1, 3] to the first SH band
        params.append(("sh0", torch.nn.Parameter(colors[:, :1, :]), sh0_lr))
        params.append(("shN", torch.nn.Parameter(colors[:, 1:, :]), shN_lr))
    else:
        # Features will be used for appearance and view-dependent shading
        features = torch.rand(N, feature_dim)  # [N, feature_dim]
        params.append(("features", torch.nn.Parameter(features), sh0_lr))
        colors = torch.logit(rgbs)  # [N, 3]
        params.append(("colors", torch.nn.Parameter(colors), sh0_lr))
    
    splats = torch.nn.ParameterDict({n: v for n, v, _ in params}).to(device)
    
    # Scale learning rate based on batch size
    BS = batch_size * world_size
    optimizer_class = None
    if sparse_grad:
        optimizer_class = torch.optim.SparseAdam
    elif visible_adam:
        from gsplat.optimizers import SelectiveAdam
        optimizer_class = SelectiveAdam
    else:
        optimizer_class = torch.optim.Adam
    
    optimizers = {
        name: optimizer_class(
            [{"params": splats[name], "lr": lr * math.sqrt(BS), "name": name}],
            eps=1e-15 / math.sqrt(BS),
            betas=(1 - BS * (1 - 0.9), 1 - BS * (1 - 0.999)),
        )
        for name, _, lr in params
    }
    
    return splats, optimizers


def estimate_scene_bounds(points: np.ndarray, margin: float = 0.1) -> np.ndarray:
    """
    Estimate scene bounds from point cloud.
    
    Args:
        points: Point cloud [N, 3]
        margin: Margin to add around points
    
    Returns:
        bounds: Scene bounds [2, 3] (min, max)
    """
    min_bounds = points.min(axis=0)
    max_bounds = points.max(axis=0)
    
    # Add margin
    extent = max_bounds - min_bounds
    min_bounds -= margin * extent
    max_bounds += margin * extent
    
    bounds = np.stack([min_bounds, max_bounds], axis=0)
    return bounds


def compute_scene_scale(points: np.ndarray, percentile: float = 95.0) -> float:
    """
    Compute scene scale from point cloud.
    
    Args:
        points: Point cloud [N, 3]
        percentile: Percentile to use for scale computation
    
    Returns:
        scale: Scene scale
    """
    center = points.mean(axis=0)
    distances = np.linalg.norm(points - center, axis=1)
    scale = np.percentile(distances, percentile)
    return max(scale, 1e-6)  # Avoid zero scale


def filter_points_by_confidence(
    points: np.ndarray,
    confidences: np.ndarray,
    threshold: float = 0.0,
    max_points: Optional[int] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Filter points by confidence and optionally limit the number of points.
    
    Args:
        points: Point cloud [N, 3]
        confidences: Point confidences [N,] or [N, 1]
        threshold: Minimum confidence threshold
        max_points: Maximum number of points to keep
    
    Returns:
        filtered_points: Filtered point cloud
        filtered_confidences: Filtered confidences
    """
    if confidences.ndim > 1:
        confidences = confidences.squeeze()
    
    # Filter by confidence
    valid_mask = confidences >= threshold
    filtered_points = points[valid_mask]
    filtered_confidences = confidences[valid_mask]
    
    # Limit number of points if specified
    if max_points is not None and len(filtered_points) > max_points:
        # Keep points with highest confidence
        indices = np.argsort(filtered_confidences)[-max_points:]
        filtered_points = filtered_points[indices]
        filtered_confidences = filtered_confidences[indices]
    
    return filtered_points, filtered_confidences


def create_rgb_from_confidence(confidences: np.ndarray, colormap: str = "viridis") -> np.ndarray:
    """
    Create RGB colors from confidence values.
    
    Args:
        confidences: Confidence values [N,] or [N, 1]
        colormap: Colormap to use
    
    Returns:
        rgb: RGB colors [N, 3]
    """
    if confidences.ndim > 1:
        confidences = confidences.squeeze()
    
    # Normalize confidences to [0, 1]
    conf_min, conf_max = confidences.min(), confidences.max()
    if conf_max > conf_min:
        conf_norm = (confidences - conf_min) / (conf_max - conf_min)
    else:
        conf_norm = np.ones_like(confidences)
    
    if colormap == "viridis":
        # Simple viridis-like colormap
        rgb = np.zeros((len(conf_norm), 3))
        rgb[:, 0] = conf_norm  # Red channel
        rgb[:, 1] = conf_norm ** 0.5  # Green channel
        rgb[:, 2] = conf_norm ** 2  # Blue channel
    elif colormap == "grayscale":
        rgb = np.stack([conf_norm] * 3, axis=1)
    else:
        # Default to grayscale
        rgb = np.stack([conf_norm] * 3, axis=1)
    
    return rgb
