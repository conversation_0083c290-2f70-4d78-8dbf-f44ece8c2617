import numpy as np
import torch
from typing import Tuple, Optional


def estimate_camera_intrinsics(
    width: int, 
    height: int, 
    fov_deg: float = 60.0,
    method: str = "fov"
) -> np.ndarray:
    """
    Estimate camera intrinsics from image dimensions.
    
    Args:
        width: Image width in pixels
        height: Image height in pixels
        fov_deg: Field of view in degrees (for 'fov' method)
        method: Method to estimate intrinsics ('fov', 'standard', 'wide')
    
    Returns:
        K: Camera intrinsics matrix [3, 3]
    """
    if method == "fov":
        # Use specified field of view
        fov_rad = np.deg2rad(fov_deg)
        fx = fy = width / (2.0 * np.tan(fov_rad / 2.0))
    elif method == "standard":
        # Standard camera assumption: focal length ~ image width
        fx = fy = width
    elif method == "wide":
        # Wide angle camera assumption
        fx = fy = width * 0.7
    else:
        raise ValueError(f"Unknown method: {method}")
    
    # Principal point at image center
    cx = width / 2.0
    cy = height / 2.0
    
    K = np.array([
        [fx, 0.0, cx],
        [0.0, fy, cy],
        [0.0, 0.0, 1.0]
    ])
    
    return K


def estimate_intrinsics_from_points_and_pose(
    points_3d: np.ndarray,
    points_2d: np.ndarray,
    pose: np.ndarray,
    width: int,
    height: int
) -> np.ndarray:
    """
    Estimate camera intrinsics using 3D-2D point correspondences and camera pose.
    
    Args:
        points_3d: 3D points in world coordinates [N, 3]
        points_2d: Corresponding 2D points in image coordinates [N, 2]
        pose: Camera pose (world-to-camera transform) [4, 4]
        width: Image width
        height: Image height
    
    Returns:
        K: Estimated camera intrinsics [3, 3]
    """
    if len(points_3d) < 4:
        # Fall back to FOV-based estimation
        return estimate_camera_intrinsics(width, height)
    
    # Transform 3D points to camera coordinates
    points_3d_hom = np.concatenate([points_3d, np.ones((len(points_3d), 1))], axis=1)
    points_cam = (pose @ points_3d_hom.T).T[:, :3]  # [N, 3]
    
    # Filter points behind camera
    valid_mask = points_cam[:, 2] > 0.1
    if valid_mask.sum() < 4:
        return estimate_camera_intrinsics(width, height)
    
    points_cam = points_cam[valid_mask]
    points_2d_valid = points_2d[valid_mask]
    
    # Project to normalized image coordinates
    points_norm = points_cam[:, :2] / points_cam[:, 2:3]  # [N, 2]
    
    # Solve for intrinsics using least squares
    # p_image = K @ p_norm_hom
    # [u, v, 1]^T = [fx*x + cx, fy*y + cy, 1]^T
    
    A = []
    b = []
    
    for i in range(len(points_norm)):
        x_norm, y_norm = points_norm[i]
        u, v = points_2d_valid[i]
        
        # u = fx * x_norm + cx
        A.append([x_norm, 0, 1, 0])
        b.append(u)
        
        # v = fy * y_norm + cy
        A.append([0, y_norm, 0, 1])
        b.append(v)
    
    A = np.array(A)
    b = np.array(b)
    
    try:
        # Solve [fx, fy, cx, cy]
        params = np.linalg.lstsq(A, b, rcond=None)[0]
        fx, fy, cx, cy = params
        
        # Sanity checks
        if fx <= 0 or fy <= 0 or cx < 0 or cx > width or cy < 0 or cy > height:
            raise ValueError("Invalid intrinsics estimated")
        
        K = np.array([
            [fx, 0.0, cx],
            [0.0, fy, cy],
            [0.0, 0.0, 1.0]
        ])
        
        return K
        
    except:
        # Fall back to FOV-based estimation
        return estimate_camera_intrinsics(width, height)


def refine_camera_intrinsics(
    K_init: np.ndarray,
    points_3d: np.ndarray,
    points_2d: np.ndarray,
    pose: np.ndarray,
    max_iterations: int = 10,
    tolerance: float = 1e-6
) -> np.ndarray:
    """
    Refine camera intrinsics using iterative optimization.
    
    Args:
        K_init: Initial camera intrinsics [3, 3]
        points_3d: 3D points in world coordinates [N, 3]
        points_2d: Corresponding 2D points in image coordinates [N, 2]
        pose: Camera pose (world-to-camera transform) [4, 4]
        max_iterations: Maximum number of iterations
        tolerance: Convergence tolerance
    
    Returns:
        K_refined: Refined camera intrinsics [3, 3]
    """
    K = K_init.copy()
    
    for iteration in range(max_iterations):
        # Project 3D points using current intrinsics
        points_3d_hom = np.concatenate([points_3d, np.ones((len(points_3d), 1))], axis=1)
        points_cam = (pose @ points_3d_hom.T).T[:, :3]
        
        # Filter points behind camera
        valid_mask = points_cam[:, 2] > 0.1
        if valid_mask.sum() < 4:
            break
            
        points_cam_valid = points_cam[valid_mask]
        points_2d_valid = points_2d[valid_mask]
        
        # Project to image coordinates
        points_proj_hom = (K @ points_cam_valid.T).T
        points_proj = points_proj_hom[:, :2] / points_proj_hom[:, 2:3]
        
        # Compute reprojection error
        error = points_proj - points_2d_valid
        rmse = np.sqrt(np.mean(error**2))
        
        if rmse < tolerance:
            break
        
        # Simple gradient descent update (simplified)
        # In practice, you might want to use more sophisticated optimization
        learning_rate = 0.01
        
        # Compute gradients (simplified approximation)
        grad_fx = np.mean(error[:, 0] * points_cam_valid[:, 0] / points_cam_valid[:, 2])
        grad_fy = np.mean(error[:, 1] * points_cam_valid[:, 1] / points_cam_valid[:, 2])
        grad_cx = np.mean(error[:, 0])
        grad_cy = np.mean(error[:, 1])
        
        # Update intrinsics
        K[0, 0] -= learning_rate * grad_fx
        K[1, 1] -= learning_rate * grad_fy
        K[0, 2] -= learning_rate * grad_cx
        K[1, 2] -= learning_rate * grad_cy
    
    return K


def validate_camera_intrinsics(K: np.ndarray, width: int, height: int) -> bool:
    """
    Validate camera intrinsics matrix.
    
    Args:
        K: Camera intrinsics matrix [3, 3]
        width: Image width
        height: Image height
    
    Returns:
        valid: True if intrinsics are reasonable
    """
    fx, fy = K[0, 0], K[1, 1]
    cx, cy = K[0, 2], K[1, 2]
    
    # Check focal lengths are positive and reasonable
    if fx <= 0 or fy <= 0:
        return False
    
    if fx > 10 * width or fy > 10 * height:
        return False
    
    if fx < 0.1 * width or fy < 0.1 * height:
        return False
    
    # Check principal point is within image bounds (with some margin)
    margin = 0.2
    if cx < -margin * width or cx > (1 + margin) * width:
        return False
    
    if cy < -margin * height or cy > (1 + margin) * height:
        return False
    
    return True


def convert_pose_format(T_WC: np.ndarray) -> np.ndarray:
    """
    Convert camera pose from world-to-camera to camera-to-world format.
    
    Args:
        T_WC: World-to-camera transformation matrix [4, 4]
    
    Returns:
        T_CW: Camera-to-world transformation matrix [4, 4]
    """
    return np.linalg.inv(T_WC)


def get_camera_frustum_points(K: np.ndarray, pose: np.ndarray, width: int, height: int, depth: float = 1.0) -> np.ndarray:
    """
    Get 3D points representing camera frustum corners.
    
    Args:
        K: Camera intrinsics [3, 3]
        pose: Camera-to-world pose [4, 4]
        width: Image width
        height: Image height
        depth: Depth at which to compute frustum corners
    
    Returns:
        corners: 3D frustum corner points [8, 3]
    """
    # Image corners in pixel coordinates
    corners_2d = np.array([
        [0, 0],
        [width, 0],
        [width, height],
        [0, height]
    ])
    
    # Convert to normalized camera coordinates
    corners_2d_hom = np.concatenate([corners_2d, np.ones((4, 1))], axis=1)
    K_inv = np.linalg.inv(K)
    corners_norm = (K_inv @ corners_2d_hom.T).T
    
    # Create 3D points at two depths
    corners_3d = []
    for d in [0.1, depth]:
        corners_at_depth = corners_norm * d
        corners_at_depth = np.concatenate([corners_at_depth[:, :2], np.full((4, 1), d)], axis=1)
        corners_3d.append(corners_at_depth)
    
    corners_3d = np.concatenate(corners_3d, axis=0)  # [8, 3]
    
    # Transform to world coordinates
    corners_3d_hom = np.concatenate([corners_3d, np.ones((8, 1))], axis=1)
    corners_world = (pose @ corners_3d_hom.T).T[:, :3]
    
    return corners_world
