#!/usr/bin/env python3
"""
Test script for keyframes 3DGS implementation.
This script tests the data loading, training, and rendering pipeline.
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# Add current directory to path for imports
sys.path.append('.')

def test_data_loading():
    """Test keyframes data loading."""
    print("=" * 50)
    print("Testing data loading...")
    
    try:
        from keyframes_dataset import KeyframesDataset, KeyframesParser
        
        # Test single file loading
        data_path = "data/keyframes/24.pkl"
        if not os.path.exists(data_path):
            print(f"Warning: Test file {data_path} not found, skipping data loading test")
            return False
        
        dataset = KeyframesDataset(
            data_path=data_path,
            confidence_threshold=0.0,
            max_points=10000,  # Limit for testing
            normalize_points=True,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        print(f"✓ Dataset loaded: {len(dataset)} frames")
        
        # Test data item
        data_item = dataset[0]
        print(f"✓ Data item keys: {list(data_item.keys())}")
        print(f"✓ Image shape: {data_item['image'].shape}")
        print(f"✓ Camera pose shape: {data_item['camtoworld'].shape}")
        print(f"✓ Camera intrinsics shape: {data_item['K'].shape}")
        
        # Test parser
        parser = KeyframesParser(dataset)
        print(f"✓ Parser created with {len(parser.points)} points")
        print(f"✓ Scene scale: {parser.scene_scale:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_initialization():
    """Test 3DGS model initialization."""
    print("=" * 50)
    print("Testing model initialization...")
    
    try:
        from keyframes_dataset import KeyframesDataset, KeyframesParser
        from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
        
        # Create minimal dataset
        data_path = "data/keyframes/24.pkl"
        if not os.path.exists(data_path):
            print(f"Warning: Test file {data_path} not found, skipping model test")
            return False
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        dataset = KeyframesDataset(
            data_path=data_path,
            max_points=1000,  # Very small for testing
            device=device
        )
        parser = KeyframesParser(dataset)
        
        # Initialize model
        splats, optimizers = create_splats_with_optimizers_keyframes(
            parser,
            device=device,
        )
        
        print(f"✓ Model initialized with {len(splats['means'])} Gaussians")
        print(f"✓ Splat parameters: {list(splats.keys())}")
        print(f"✓ Optimizers: {list(optimizers.keys())}")
        
        # Test parameter shapes
        print(f"✓ Means shape: {splats['means'].shape}")
        print(f"✓ Scales shape: {splats['scales'].shape}")
        print(f"✓ Quats shape: {splats['quats'].shape}")
        print(f"✓ Opacities shape: {splats['opacities'].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_rendering():
    """Test rendering pipeline."""
    print("=" * 50)
    print("Testing rendering...")
    
    try:
        from keyframes_dataset import KeyframesDataset, KeyframesParser
        from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
        from gsplat.rendering import rasterization
        
        data_path = "data/keyframes/24.pkl"
        if not os.path.exists(data_path):
            print(f"Warning: Test file {data_path} not found, skipping rendering test")
            return False
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        dataset = KeyframesDataset(
            data_path=data_path,
            max_points=1000,
            device=device
        )
        parser = KeyframesParser(dataset)
        
        # Initialize model
        splats, _ = create_splats_with_optimizers_keyframes(
            parser,
            device=device,
        )
        
        # Get camera parameters
        data_item = dataset[0]
        camtoworld = data_item['camtoworld']  # [1, 4, 4]
        K = data_item['K']  # [1, 3, 3]
        height, width = 288, 512  # From the data
        
        # Render
        means = splats["means"]
        quats = splats["quats"]
        scales = torch.exp(splats["scales"])
        opacities = torch.sigmoid(splats["opacities"])
        colors = torch.cat([splats["sh0"], splats["shN"]], 1)
        
        render_colors, render_alphas, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld),
            Ks=K,
            width=width,
            height=height,
            sh_degree=3,  # Add sh_degree parameter
        )
        
        print(f"✓ Rendering successful")
        print(f"✓ Rendered image shape: {render_colors.shape}")
        print(f"✓ Rendered alpha shape: {render_alphas.shape}")
        print(f"✓ Visible Gaussians: {(info['radii'] > 0).sum().item()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Rendering test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_step():
    """Test a single training step."""
    print("=" * 50)
    print("Testing training step...")
    
    try:
        from keyframes_trainer import KeyframesTrainer, KeyframesConfig
        
        data_path = "data/keyframes/24.pkl"
        if not os.path.exists(data_path):
            print(f"Warning: Test file {data_path} not found, skipping training test")
            return False
        
        # Create minimal config
        cfg = KeyframesConfig(
            data_path=data_path,
            result_dir="test_results",
            max_steps=1,  # Just one step
            max_points=1000,
            confidence_threshold=0.0,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        # Create trainer
        trainer = KeyframesTrainer(cfg)
        
        # Test single forward pass
        dataloader = torch.utils.data.DataLoader(
            trainer.dataset, batch_size=1, shuffle=False
        )
        
        data = next(iter(dataloader))
        camtoworlds = data["camtoworld"]
        Ks = data["K"]
        images = data["image"]

        # Debug shapes
        print(f"Images shape: {images.shape}")
        print(f"Camtoworlds shape: {camtoworlds.shape}")
        print(f"Ks shape: {Ks.shape}")

        # Handle potential extra batch dimension
        if images.dim() == 5:  # [batch, 1, 3, H, W]
            images = images.squeeze(1)  # [batch, 3, H, W]
        if camtoworlds.dim() == 4:  # [batch, 1, 4, 4]
            camtoworlds = camtoworlds.squeeze(1)  # [batch, 4, 4]
        if Ks.dim() == 4:  # [batch, 1, 3, 3]
            Ks = Ks.squeeze(1)  # [batch, 3, 3]

        pixels = images.permute(0, 2, 3, 1)
        height, width = pixels.shape[1:3]
        
        # Forward pass
        renders, alphas, info = trainer.rasterize_splats(
            camtoworlds=camtoworlds,
            Ks=Ks,
            width=width,
            height=height,
        )
        
        # Compute loss
        import torch.nn.functional as F
        l1loss = F.l1_loss(renders, pixels)
        
        print(f"✓ Forward pass successful")
        print(f"✓ L1 loss: {l1loss.item():.6f}")
        print(f"✓ Rendered shape: {renders.shape}")
        
        # Clean up test results
        import shutil
        if os.path.exists("test_results"):
            shutil.rmtree("test_results")
        
        return True
        
    except Exception as e:
        print(f"✗ Training step test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_frame_loading():
    """Test multi-frame data loading."""
    print("=" * 50)
    print("Testing multi-frame loading...")
    
    try:
        from keyframes_dataset import KeyframesDataset
        
        data_dir = "data/keyframes"
        if not os.path.exists(data_dir):
            print(f"Warning: Data directory {data_dir} not found, skipping multi-frame test")
            return False
        
        # Test multi-frame dataset
        dataset = KeyframesDataset(
            data_path=data_dir,
            split="train",
            multi_frame_mode=True,
            test_every=8,
            max_points=5000,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        print(f"✓ Multi-frame dataset loaded: {len(dataset)} frames")
        
        # Test validation split
        val_dataset = KeyframesDataset(
            data_path=data_dir,
            split="val",
            multi_frame_mode=True,
            test_every=8,
            max_points=5000,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        print(f"✓ Validation dataset loaded: {len(val_dataset)} frames")
        
        return True
        
    except Exception as e:
        print(f"✗ Multi-frame loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("Starting keyframes 3DGS tests...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name()}")
    
    tests = [
        ("Data Loading", test_data_loading),
        ("Model Initialization", test_model_initialization),
        ("Rendering", test_rendering),
        ("Training Step", test_training_step),
        ("Multi-frame Loading", test_multi_frame_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The keyframes 3DGS implementation is ready to use.")
        print("\nNext steps:")
        print("1. Run single-frame training: python keyframes_trainer.py --data_path data/keyframes/24.pkl")
        print("2. Run multi-frame training: python train_multi_keyframes.py --data_path data/keyframes")
        print("3. Render results: python keyframes_renderer.py --ckpt_path results/keyframes/ckpts/ckpt_9999.pt")
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed. Please check the errors above.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
