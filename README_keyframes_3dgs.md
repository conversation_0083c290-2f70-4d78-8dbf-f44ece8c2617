# Keyframes 3D Gaussian Splatting

基于gsplat库实现的keyframes数据3D Gaussian Splatting训练和渲染系统。

## 功能特性

- ✅ 支持keyframes pkl文件格式的数据加载
- ✅ 使用点云数据(X_canon)和置信度(C)进行3DGS初始化
- ✅ 自动估算相机内参矩阵
- ✅ 单文件和多文件训练模式
- ✅ 任意视角渲染和轨迹生成
- ✅ 完整的训练和评估流程

## 环境要求

```bash
# 激活conda环境
conda activate 3dgs

# 确保已安装以下依赖
pip install torch torchvision
pip install numpy imageio tqdm tyro pyyaml
pip install torchmetrics
```

## 数据格式

keyframes pkl文件应包含以下字段：
- `frame_id`: 帧ID
- `img`: 处理后图像 [1, 3, H, W]
- `X_canon`: 点云坐标 [N, 3]
- `C`: 点云置信度 [N, 1]
- `T_WC`: 相机位姿矩阵 [1, 4, 4]
- `img_shape`: 图像尺寸 [[H, W]]
- `uimg`: 原始图像 [H, W, 3]
- `img_path`: 图像路径

## 快速开始

### 1. 测试安装

```bash
python test_keyframes_3dgs.py
```

### 2. 单文件训练

```bash
# 使用24.pkl进行训练
python keyframes_trainer.py \
    --data_path data/keyframes/24.pkl \
    --result_dir results/single_frame \
    --max_steps 10000 \
    --confidence_threshold 0.0
```

### 3. 多文件训练

```bash
# 使用多个keyframe文件进行训练
python train_multi_keyframes.py \
    --data_path data/keyframes \
    --result_dir results/multi_frame \
    --max_steps 30000 \
    --max_frames 20 \
    --confidence_threshold 5.0
```

### 4. 渲染结果

```bash
# 渲染训练结果
python keyframes_renderer.py \
    --ckpt_path results/single_frame/ckpts/ckpt_9999.pt \
    --result_dir results/render \
    --render_mode turntable \
    --num_frames 120
```

## 配置参数

### 训练参数

- `data_path`: pkl文件路径或包含pkl文件的目录
- `result_dir`: 结果保存目录
- `max_steps`: 训练步数
- `confidence_threshold`: 点云置信度阈值
- `max_points`: 最大点数限制
- `sh_degree`: 球谐函数阶数
- `init_opacity`: 初始透明度
- `means_lr`: 位置学习率
- `scales_lr`: 尺度学习率

### 渲染参数

- `ckpt_path`: 模型检查点路径
- `render_mode`: 渲染模式 ("turntable", "trajectory", "novel_view")
- `num_frames`: 渲染帧数
- `width/height`: 渲染分辨率
- `save_video`: 是否保存视频

## 文件结构

```
├── keyframes_dataset.py          # 数据加载器
├── keyframes_trainer.py          # 单文件训练脚本
├── train_multi_keyframes.py      # 多文件训练脚本
├── keyframes_renderer.py         # 渲染脚本
├── keyframes_trainer_utils.py    # 训练工具函数
├── camera_utils.py               # 相机工具函数
├── test_keyframes_3dgs.py        # 测试脚本
└── README_keyframes_3dgs.md      # 说明文档
```

## 使用示例

### 基础训练流程

```python
from keyframes_trainer import KeyframesTrainer, KeyframesConfig

# 创建配置
cfg = KeyframesConfig(
    data_path="data/keyframes/24.pkl",
    result_dir="results/test",
    max_steps=5000,
    confidence_threshold=0.0
)

# 创建训练器并训练
trainer = KeyframesTrainer(cfg)
trainer.train()
```

### 自定义数据加载

```python
from keyframes_dataset import KeyframesDataset

# 加载数据集
dataset = KeyframesDataset(
    data_path="data/keyframes/24.pkl",
    confidence_threshold=5.0,
    max_points=50000,
    normalize_points=True
)

# 获取数据项
data_item = dataset[0]
print(f"Image shape: {data_item['image'].shape}")
print(f"Points: {len(dataset.points_3d)}")
```

### 渲染自定义视角

```python
from keyframes_renderer import KeyframesRenderer, RenderConfig

# 创建渲染器
config = RenderConfig(
    ckpt_path="results/test/ckpts/ckpt_4999.pt",
    render_mode="turntable",
    num_frames=60
)

renderer = KeyframesRenderer(config)
renderer.render_trajectory()
```

## 性能优化建议

1. **内存管理**：
   - 使用`max_points`限制点云大小
   - 调整`confidence_threshold`过滤低质量点
   - 使用`packed=True`减少内存使用

2. **训练效率**：
   - 多文件训练时使用`max_frames`限制帧数
   - 调整学习率和训练步数
   - 使用适当的正则化参数

3. **渲染质量**：
   - 增加`sh_degree`提高视觉质量
   - 调整`init_opacity`和`init_scale`
   - 使用`antialiased=True`改善渲染效果

## 故障排除

### 常见问题

1. **CUDA内存不足**：
   - 减少`max_points`
   - 降低图像分辨率
   - 使用`packed=True`

2. **训练不收敛**：
   - 检查相机内参估算
   - 调整学习率
   - 增加训练步数

3. **渲染结果异常**：
   - 检查场景归一化
   - 验证相机位姿
   - 调整渲染参数

### 调试模式

```bash
# 运行测试脚本检查各个组件
python test_keyframes_3dgs.py

# 使用小数据集快速测试
python keyframes_trainer.py \
    --data_path data/keyframes/24.pkl \
    --max_steps 100 \
    --max_points 1000
```

## 扩展功能

- 支持更多相机模型
- 添加深度监督
- 实现外观优化
- 支持压缩和导出

## 许可证

本实现基于gsplat库，遵循相应的开源许可证。
