#!/usr/bin/env python3
"""
Multi-keyframe training script for 3D Gaussian Splatting.
This script supports training with multiple keyframe files for multi-view reconstruction.
"""

import os
import glob
from dataclasses import dataclass, field
from typing import List
import tyro

from keyframes_trainer import KeyframesTrainer, KeyframesConfig


@dataclass
class MultiKeyframesConfig(KeyframesConfig):
    """Configuration for multi-keyframe 3DGS training."""
    
    # Override data settings for multi-frame mode
    data_path: str = "data/keyframes"  # Directory containing pkl files
    result_dir: str = "results/multi_keyframes"
    
    # Multi-frame specific settings
    multi_frame_mode: bool = True  # Enable multi-frame training
    test_every: int = 8  # Every N-th frame for validation
    max_frames: int = 50  # Maximum number of frames to use (0 = use all)
    
    # Training settings adjusted for multi-view
    max_steps: int = 30_000  # More steps for multi-view
    batch_size: int = 1  # Keep batch size 1 for memory efficiency
    eval_steps: List[int] = field(default_factory=lambda: [10_000, 20_000, 30_000])
    save_steps: List[int] = field(default_factory=lambda: [10_000, 20_000, 30_000])
    
    # Optimization settings for multi-view
    means_lr: float = 1.6e-4
    scales_lr: float = 5e-3
    opacities_lr: float = 5e-2
    
    # Point cloud settings
    confidence_threshold: float = 5.0  # Higher threshold for multi-view
    max_points: int = 100_000  # Limit points for memory management
    
    # Regularization for multi-view
    opacity_reg: float = 0.01
    scale_reg: float = 0.01


class MultiKeyframesTrainer(KeyframesTrainer):
    """Extended trainer for multi-keyframe training."""
    
    def __init__(self, cfg: MultiKeyframesConfig):
        # Update dataset creation to use multi-frame mode
        self.multi_cfg = cfg
        super().__init__(cfg)
    
    def _create_dataset(self):
        """Override dataset creation for multi-frame mode."""
        from keyframes_dataset import KeyframesDataset
        
        # Get list of pkl files
        if os.path.isdir(self.cfg.data_path):
            pkl_files = glob.glob(os.path.join(self.cfg.data_path, "*.pkl"))
            pkl_files.sort()
            
            # Limit number of frames if specified
            if self.multi_cfg.max_frames > 0:
                pkl_files = pkl_files[:self.multi_cfg.max_frames]
            
            print(f"Found {len(pkl_files)} pkl files")
            
            # Create train and validation datasets
            self.trainset = KeyframesDataset(
                data_path=pkl_files,
                split="train",
                confidence_threshold=self.cfg.confidence_threshold,
                max_points=self.cfg.max_points,
                normalize_points=self.cfg.normalize_points,
                device=self.device,
                multi_frame_mode=True,
                test_every=self.multi_cfg.test_every
            )
            
            self.valset = KeyframesDataset(
                data_path=pkl_files,
                split="val",
                confidence_threshold=self.cfg.confidence_threshold,
                max_points=self.cfg.max_points,
                normalize_points=self.cfg.normalize_points,
                device=self.device,
                multi_frame_mode=True,
                test_every=self.multi_cfg.test_every
            )
            
            print(f"Train set: {len(self.trainset)} frames")
            print(f"Val set: {len(self.valset)} frames")
            
        else:
            # Single file mode
            self.trainset = KeyframesDataset(
                data_path=self.cfg.data_path,
                split="train",
                confidence_threshold=self.cfg.confidence_threshold,
                max_points=self.cfg.max_points,
                normalize_points=self.cfg.normalize_points,
                device=self.device,
                multi_frame_mode=False
            )
            self.valset = self.trainset  # Use same for validation
    
    def train(self):
        """Override training to handle multi-frame datasets."""
        # Create datasets
        self._create_dataset()
        
        # Update parser to use training dataset
        from keyframes_dataset import KeyframesParser
        self.parser = KeyframesParser(self.trainset)
        self.scene_scale = self.parser.scene_scale
        
        # Recreate model with updated parser
        from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
        self.splats, self.optimizers = create_splats_with_optimizers_keyframes(
            self.parser,
            init_opacity=self.cfg.init_opacity,
            init_scale=self.cfg.init_scale,
            means_lr=self.cfg.means_lr,
            scales_lr=self.cfg.scales_lr,
            opacities_lr=self.cfg.opacities_lr,
            quats_lr=self.cfg.quats_lr,
            sh0_lr=self.cfg.sh0_lr,
            shN_lr=self.cfg.shN_lr,
            scene_scale=self.scene_scale,
            sh_degree=self.cfg.sh_degree,
            device=self.device,
        )
        
        print(f"Model initialized with {len(self.splats['means'])} Gaussians")
        
        # Initialize strategy
        self.cfg.strategy.check_sanity(self.splats, self.optimizers)
        if isinstance(self.cfg.strategy, self.cfg.strategy.__class__):
            if hasattr(self.cfg.strategy, 'initialize_state'):
                if 'scene_scale' in self.cfg.strategy.initialize_state.__code__.co_varnames:
                    self.strategy_state = self.cfg.strategy.initialize_state(scene_scale=self.scene_scale)
                else:
                    self.strategy_state = self.cfg.strategy.initialize_state()
        
        # Call parent training method
        super().train()
    
    @torch.no_grad()
    def eval(self, step: int):
        """Evaluate on validation set."""
        print(f"Evaluating at step {step}...")
        
        # Use validation dataset
        dataloader = torch.utils.data.DataLoader(
            self.valset, batch_size=1, shuffle=False
        )
        
        metrics = defaultdict(list)
        for i, data in enumerate(dataloader):
            camtoworlds = data["camtoworld"]
            Ks = data["K"]
            images = data["image"]
            pixels = images.permute(0, 2, 3, 1)
            height, width = pixels.shape[1:3]
            
            # Render
            colors, _, _ = self.rasterize_splats(
                camtoworlds=camtoworlds,
                Ks=Ks,
                width=width,
                height=height,
                sh_degree=self.cfg.sh_degree,
                near_plane=self.cfg.near_plane,
                far_plane=self.cfg.far_plane,
            )
            
            colors = torch.clamp(colors, 0.0, 1.0)
            
            # Save rendered image
            canvas = torch.cat([pixels, colors], dim=2).squeeze(0).cpu().numpy()
            canvas = (canvas * 255).astype(np.uint8)
            imageio.imwrite(
                f"{self.render_dir}/eval_step{step}_{i:04d}.png", canvas
            )
            
            # Compute metrics
            pixels_p = pixels.permute(0, 3, 1, 2)
            colors_p = colors.permute(0, 3, 1, 2)
            metrics["psnr"].append(self.psnr(colors_p, pixels_p))
            metrics["ssim"].append(self.ssim(colors_p, pixels_p))
            metrics["lpips"].append(self.lpips(colors_p, pixels_p))
        
        # Aggregate metrics
        stats = {k: torch.stack(v).mean().item() for k, v in metrics.items()}
        stats["num_GS"] = len(self.splats["means"])
        
        print(f"PSNR: {stats['psnr']:.3f}, SSIM: {stats['ssim']:.4f}, "
              f"LPIPS: {stats['lpips']:.3f}, GS: {stats['num_GS']}")
        
        # Save stats
        with open(f"{self.stats_dir}/eval_step{step:04d}.json", "w") as f:
            json.dump(stats, f)
        
        # Log to tensorboard
        for k, v in stats.items():
            self.writer.add_scalar(f"eval/{k}", v, step)
        self.writer.flush()


def main():
    """Main function for multi-keyframe training."""
    cfg = tyro.cli(MultiKeyframesConfig)
    
    print("Starting multi-keyframe 3DGS training...")
    print(f"Config: {cfg}")
    
    # Check if data directory exists
    if not os.path.exists(cfg.data_path):
        raise ValueError(f"Data path does not exist: {cfg.data_path}")
    
    trainer = MultiKeyframesTrainer(cfg)
    trainer.train()
    
    print("Multi-keyframe training completed!")


if __name__ == "__main__":
    # Add missing imports
    import torch
    import json
    import imageio
    from collections import defaultdict
    
    main()
