#!/usr/bin/env python3
"""
保守的keyframes 3DGS训练脚本，避免过度修剪
"""

import tyro
from keyframes_trainer import KeyframesTrainer, KeyframesConfig
from gsplat.strategy import DefaultStrategy


def main():
    """保守的训练配置"""
    
    # 创建保守的策略，避免过度修剪
    conservative_strategy = DefaultStrategy(
        prune_opa=0.001,        # 降低透明度修剪阈值
        grow_grad2d=0.0005,     # 增加梯度阈值
        grow_scale3d=0.02,      # 增加3D尺度阈值
        grow_scale2d=0.1,       # 增加2D尺度阈值
        prune_scale3d=0.2,      # 增加修剪尺度阈值
        prune_scale2d=0.3,      # 增加修剪尺度阈值
        refine_start_iter=1000, # 延迟开始修剪
        refine_stop_iter=8000,  # 提前停止修剪
        reset_every=5000,       # 减少重置频率
        refine_every=200,       # 减少修剪频率
        verbose=True
    )
    
    # 创建保守的配置
    cfg = KeyframesConfig(
        # 数据设置
        data_path="data/keyframes/24.pkl",
        result_dir="results/keyframes_conservative",
        
        # 训练设置
        max_steps=10000,
        batch_size=1,
        eval_steps=[2000, 5000, 10000],
        save_steps=[2000, 5000, 10000],
        
        # 模型设置 - 温和改进
        init_opacity=0.3,      # 适中的初始透明度
        init_scale=1.5,        # 适中的初始尺度
        
        # 学习率设置 - 适中增加
        means_lr=3.2e-4,       # 增加2倍
        scales_lr=1e-2,        # 增加2倍  
        opacities_lr=1e-1,     # 增加2倍
        quats_lr=2e-3,         # 增加2倍
        sh0_lr=5e-3,           # 增加2倍
        shN_lr=2.5e-4,         # 增加2倍
        
        # 损失函数设置
        ssim_lambda=0.15,      # 适中的SSIM权重
        
        # 数据处理 - 更保守
        confidence_threshold=2.0,  # 适中的置信度阈值
        max_points=80000,          # 更多点数
        normalize_points=True,
        
        # 正则化 - 减少正则化
        opacity_reg=0.001,     # 减少透明度正则化
        scale_reg=0.001,       # 减少尺度正则化
        
        # 策略
        strategy=conservative_strategy,
        
        # 日志设置
        tb_every=100,
        tb_save_image=False,
        
        # 设备
        device="cuda"
    )
    
    print("🛡️ 开始保守的keyframes 3DGS训练...")
    print("保守策略:")
    print("  ✅ 降低修剪阈值，避免过度修剪")
    print("  ✅ 延迟开始修剪时间")
    print("  ✅ 减少修剪频率")
    print("  ✅ 适中的学习率增加")
    print("  ✅ 减少正则化强度")
    print("  ✅ 保留更多点数")
    print(f"配置: {cfg}")
    
    trainer = KeyframesTrainer(cfg)
    trainer.train()
    
    print("🎉 保守训练完成!")


if __name__ == "__main__":
    main()
