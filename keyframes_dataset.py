import os
import pickle
import numpy as np
import torch
from torch.utils.data import Dataset
from typing import Dict, List, Optional, Union
import glob


class KeyframesDataset(Dataset):
    """Dataset for loading keyframes pkl files for 3D Gaussian Splatting."""
    
    def __init__(
        self,
        data_path: Union[str, List[str]],
        split: str = "train",
        confidence_threshold: float = 0.0,
        max_points: Optional[int] = None,
        normalize_points: bool = True,
        device: str = "cuda",
        multi_frame_mode: bool = False,
        test_every: int = 8
    ):
        """
        Initialize KeyframesDataset.

        Args:
            data_path: Path to pkl file or directory containing pkl files, or list of pkl files
            split: Dataset split ("train", "val", "test")
            confidence_threshold: Minimum confidence threshold for points
            max_points: Maximum number of points to use (for memory management)
            normalize_points: Whether to normalize point cloud to unit sphere
            device: Device to load tensors on
            multi_frame_mode: If True, use multiple frames for training; if False, use single frame
            test_every: Every N-th frame is used for testing (when multi_frame_mode=True)
        """
        self.split = split
        self.confidence_threshold = confidence_threshold
        self.max_points = max_points
        self.normalize_points = normalize_points
        self.device = device
        self.multi_frame_mode = multi_frame_mode
        self.test_every = test_every

        # Load pkl files
        self.pkl_files = self._get_pkl_files(data_path)

        # Split files for train/val/test if multi_frame_mode is enabled
        if self.multi_frame_mode and len(self.pkl_files) > 1:
            self.pkl_files = self._split_files(self.pkl_files, split, test_every)

        print(f"Found {len(self.pkl_files)} pkl files for {split} split")
        
        # Load and process data
        self.data_list = []
        self.points_3d = None
        self.points_rgb = None
        self.scene_scale = 1.0
        self.scene_center = np.array([0.0, 0.0, 0.0])
        
        self._load_data()
        
    def _get_pkl_files(self, data_path: Union[str, List[str]]) -> List[str]:
        """Get list of pkl files to load."""
        if isinstance(data_path, list):
            return data_path
        elif os.path.isfile(data_path) and data_path.endswith('.pkl'):
            return [data_path]
        elif os.path.isdir(data_path):
            pkl_files = glob.glob(os.path.join(data_path, "*.pkl"))
            pkl_files.sort()
            return pkl_files
        else:
            raise ValueError(f"Invalid data_path: {data_path}")

    def _split_files(self, pkl_files: List[str], split: str, test_every: int) -> List[str]:
        """Split pkl files into train/val/test sets."""
        if split == "train":
            # Use all files except test files
            return [f for i, f in enumerate(pkl_files) if i % test_every != 0]
        elif split == "val" or split == "test":
            # Use every test_every-th file for validation/testing
            return [f for i, f in enumerate(pkl_files) if i % test_every == 0]
        else:
            return pkl_files

    def _load_data(self):
        """Load and process all pkl files."""
        all_points = []
        all_confidences = []
        
        for i, pkl_file in enumerate(self.pkl_files):
            print(f"Loading {pkl_file}...")
            
            with open(pkl_file, 'rb') as f:
                data = pickle.load(f)
            
            # Extract data
            frame_id = data['frame_id']
            img = data['img']  # [1, 3, H, W]
            uimg = data['uimg']  # [H, W, 3]
            X_canon = data['X_canon']  # [N, 3]
            C = data['C']  # [N, 1]
            T_WC = data['T_WC']  # [1, 4, 4]
            img_shape = data['img_shape']  # [[H, W]]
            img_true_shape = data['img_true_shape']  # [[H, W]]
            img_path = data['img_path']
            
            # Filter points by confidence
            if self.confidence_threshold > 0:
                valid_mask = C.squeeze() >= self.confidence_threshold
                X_canon = X_canon[valid_mask]
                C = C[valid_mask]
            
            # Collect points for global normalization
            all_points.append(X_canon)
            all_confidences.append(C)
            
            # Process image data
            if img.shape[0] == 1:  # Remove batch dimension if present
                img = img[0]  # [3, H, W]
            
            # Convert image from [-1, 1] to [0, 1] if needed
            if img.min() < 0:
                img = (img + 1.0) / 2.0
            
            # Ensure uimg is in [0, 1] range
            if uimg.max() > 1.0:
                uimg = uimg / 255.0
            
            # Convert T_WC to numpy if it's a tensor
            if isinstance(T_WC, torch.Tensor):
                T_WC = T_WC.cpu().numpy()
            
            # Remove batch dimension from T_WC if present
            if T_WC.shape[0] == 1:
                T_WC = T_WC[0]  # [4, 4]
            
            # Estimate camera intrinsics from image shape
            H, W = img_shape[0]
            K = self._estimate_camera_intrinsics(W, H)
            
            # Store processed data
            data_item = {
                'frame_id': frame_id,
                'image': torch.from_numpy(img).float(),  # [3, H, W]
                'image_raw': torch.from_numpy(uimg).float(),  # [H, W, 3]
                'points_3d': X_canon,  # [N, 3]
                'confidences': C,  # [N, 1]
                'camtoworld': T_WC,  # [4, 4]
                'K': K,  # [3, 3]
                'width': W,
                'height': H,
                'image_path': img_path,
                'pkl_file': pkl_file
            }
            
            self.data_list.append(data_item)
        
        # Combine all points for global processing
        if all_points:
            combined_points = np.concatenate(all_points, axis=0)
            combined_confidences = np.concatenate(all_confidences, axis=0)
            
            # Normalize points if requested
            if self.normalize_points:
                self.scene_center = combined_points.mean(axis=0)
                centered_points = combined_points - self.scene_center
                self.scene_scale = np.percentile(np.linalg.norm(centered_points, axis=1), 95)
                
                # Apply normalization to all data items
                for data_item in self.data_list:
                    data_item['points_3d'] = (data_item['points_3d'] - self.scene_center) / self.scene_scale
                    # Also adjust camera positions
                    data_item['camtoworld'][:3, 3] = (data_item['camtoworld'][:3, 3] - self.scene_center) / self.scene_scale
            
            # Subsample points if max_points is specified
            if self.max_points and len(combined_points) > self.max_points:
                # Use confidence-based sampling
                indices = np.argsort(combined_confidences.squeeze())[-self.max_points:]
                for data_item in self.data_list:
                    n_points = len(data_item['points_3d'])
                    if n_points > self.max_points:
                        # Sample proportionally for each frame
                        n_sample = min(self.max_points, n_points)
                        conf_indices = np.argsort(data_item['confidences'].squeeze())[-n_sample:]
                        data_item['points_3d'] = data_item['points_3d'][conf_indices]
                        data_item['confidences'] = data_item['confidences'][conf_indices]
            
            # Store global point cloud (from first frame for initialization)
            self.points_3d = self.data_list[0]['points_3d']
            # Estimate RGB from confidence (placeholder - could be improved)
            confidences = self.data_list[0]['confidences'].squeeze()
            conf_normalized = (confidences - confidences.min()) / (confidences.max() - confidences.min())
            self.points_rgb = np.stack([conf_normalized] * 3, axis=1)  # Grayscale based on confidence
    
    def _estimate_camera_intrinsics(self, width: int, height: int) -> np.ndarray:
        """Estimate camera intrinsics from image dimensions."""
        # Assume reasonable FOV (e.g., 60 degrees)
        fov_deg = 60.0
        fov_rad = np.deg2rad(fov_deg)
        
        # Calculate focal length
        fx = fy = width / (2.0 * np.tan(fov_rad / 2.0))
        
        # Principal point at image center
        cx = width / 2.0
        cy = height / 2.0
        
        K = np.array([
            [fx, 0.0, cx],
            [0.0, fy, cy],
            [0.0, 0.0, 1.0]
        ])
        
        return K
    
    def __len__(self) -> int:
        return len(self.data_list)
    
    def __getitem__(self, idx: int) -> Dict:
        """Get a data item."""
        data_item = self.data_list[idx].copy()
        
        # Convert to tensors and move to device
        for key in ['image', 'image_raw']:
            if key in data_item:
                data_item[key] = data_item[key].to(self.device)
        
        for key in ['camtoworld', 'K']:
            if key in data_item:
                data_item[key] = torch.from_numpy(data_item[key]).float().to(self.device)
        
        # Add batch dimensions where needed
        data_item['camtoworld'] = data_item['camtoworld'].unsqueeze(0)  # [1, 4, 4]
        data_item['K'] = data_item['K'].unsqueeze(0)  # [1, 3, 3]
        data_item['image'] = data_item['image'].unsqueeze(0)  # [1, 3, H, W]
        
        # Transpose image_raw to match expected format [1, H, W, 3]
        if 'image_raw' in data_item:
            data_item['image_raw'] = data_item['image_raw'].unsqueeze(0)  # [1, H, W, 3]
        
        # Add image_id for compatibility
        data_item['image_id'] = torch.tensor([idx], device=self.device)
        
        return data_item


class KeyframesParser:
    """Parser class to mimic COLMAP parser interface for compatibility with gsplat."""
    
    def __init__(self, dataset: KeyframesDataset):
        self.dataset = dataset
        
        # Extract points and colors for initialization
        self.points = dataset.points_3d.astype(np.float32)
        self.points_rgb = (dataset.points_rgb * 255).astype(np.uint8)
        
        # Scene properties
        self.scene_scale = dataset.scene_scale
        
        # Camera properties (from first frame)
        first_item = dataset.data_list[0]
        self.Ks_dict = {0: first_item['K']}
        self.imsize_dict = {0: (first_item['width'], first_item['height'])}
        
        # Camera poses
        self.camtoworlds = np.stack([item['camtoworld'] for item in dataset.data_list])
        
        print(f"KeyframesParser initialized:")
        print(f"  Points: {len(self.points)}")
        print(f"  Scene scale: {self.scene_scale:.3f}")
        print(f"  Image size: {self.imsize_dict[0]}")
