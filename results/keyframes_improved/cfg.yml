antialiased: false
batch_size: 1
camera_fov: 60.0
confidence_threshold: 5.0
data_path: data/keyframes/24.pkl
device: cuda
disable_viewer: true
eval_steps:
- 2000
- 5000
- 10000
far_plane: 10000000000.0
init_opacity: 0.5
init_scale: 2.0
max_points: 50000
max_steps: 10000
means_lr: 0.0016
near_plane: 0.01
normalize_points: true
opacities_lr: 0.5
opacity_reg: 0.01
packed: false
port: 8080
quats_lr: 0.01
result_dir: results/keyframes_improved
save_steps:
- 2000
- 5000
- 10000
scale_reg: 0.01
scales_lr: 0.05
sh0_lr: 0.025
shN_lr: 0.0025
sh_degree: 3
sh_degree_interval: 1000
ssim_lambda: 0.1
strategy: !!python/object:gsplat.strategy.default.DefaultStrategy
  absgrad: false
  grow_grad2d: 0.0002
  grow_scale2d: 0.05
  grow_scale3d: 0.01
  key_for_gradient: means2d
  pause_refine_after_reset: 0
  prune_opa: 0.005
  prune_scale2d: 0.15
  prune_scale3d: 0.1
  refine_every: 100
  refine_scale2d_stop_iter: 0
  refine_start_iter: 500
  refine_stop_iter: 15000
  reset_every: 3000
  revised_opacity: false
  verbose: true
tb_every: 50
tb_save_image: false
