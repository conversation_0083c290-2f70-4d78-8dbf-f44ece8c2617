antialiased: false
batch_size: 1
camera_fov: 60.0
confidence_threshold: 0.0
data_path: data/keyframes/24.pkl
device: cuda
disable_viewer: true
eval_steps:
- 1000
- 3000
- 5000
far_plane: 10000000000.0
init_opacity: 0.5
init_scale: 10.0
max_points: null
max_steps: 5000
means_lr: 0.00016
near_plane: 0.01
normalize_points: true
opacities_lr: 0.05
opacity_reg: 0.0
packed: false
port: 8080
quats_lr: 0.001
result_dir: results/keyframes_fixed
save_steps:
- 1000
- 3000
- 5000
scale_reg: 0.0
scales_lr: 0.005
sh0_lr: 0.0025
shN_lr: 0.000125
sh_degree: 3
sh_degree_interval: 1000
ssim_lambda: 0.2
strategy: !!python/object:gsplat.strategy.default.DefaultStrategy
  absgrad: false
  grow_grad2d: 10000000000.0
  grow_scale2d: 10000000000.0
  grow_scale3d: 10000000000.0
  key_for_gradient: means2d
  pause_refine_after_reset: 0
  prune_opa: 0.0
  prune_scale2d: 10000000000.0
  prune_scale3d: 10000000000.0
  refine_every: 50000
  refine_scale2d_stop_iter: 0
  refine_start_iter: 50000
  refine_stop_iter: 50001
  reset_every: 50000
  revised_opacity: false
  verbose: true
tb_every: 100
tb_save_image: false
