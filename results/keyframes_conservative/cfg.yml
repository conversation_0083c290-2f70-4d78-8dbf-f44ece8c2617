antialiased: false
batch_size: 1
camera_fov: 60.0
confidence_threshold: 2.0
data_path: data/keyframes/24.pkl
device: cuda
disable_viewer: true
eval_steps:
- 2000
- 5000
- 10000
far_plane: 10000000000.0
init_opacity: 0.3
init_scale: 1.5
max_points: 80000
max_steps: 10000
means_lr: 0.00032
near_plane: 0.01
normalize_points: true
opacities_lr: 0.1
opacity_reg: 0.001
packed: false
port: 8080
quats_lr: 0.002
result_dir: results/keyframes_conservative
save_steps:
- 2000
- 5000
- 10000
scale_reg: 0.001
scales_lr: 0.01
sh0_lr: 0.005
shN_lr: 0.00025
sh_degree: 3
sh_degree_interval: 1000
ssim_lambda: 0.15
strategy: !!python/object:gsplat.strategy.default.DefaultStrategy
  absgrad: false
  grow_grad2d: 0.0005
  grow_scale2d: 0.1
  grow_scale3d: 0.02
  key_for_gradient: means2d
  pause_refine_after_reset: 0
  prune_opa: 0.001
  prune_scale2d: 0.3
  prune_scale3d: 0.2
  refine_every: 200
  refine_scale2d_stop_iter: 0
  refine_start_iter: 1000
  refine_stop_iter: 8000
  reset_every: 5000
  revised_opacity: false
  verbose: true
tb_every: 100
tb_save_image: false
