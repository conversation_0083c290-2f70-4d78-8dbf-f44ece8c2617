#!/usr/bin/env python3
"""
改进的keyframes 3DGS训练脚本，解决loss不变的问题
"""

import tyro
from keyframes_trainer import KeyframesTrainer, KeyframesConfig


def main():
    """改进的训练配置"""
    
    # 创建改进的配置
    cfg = KeyframesConfig(
        # 数据设置
        data_path="data/keyframes/24.pkl",
        result_dir="results/keyframes_improved",
        
        # 训练设置
        max_steps=10000,
        batch_size=1,
        eval_steps=[2000, 5000, 10000],
        save_steps=[2000, 5000, 10000],
        
        # 模型设置 - 关键改进
        init_opacity=0.5,  # 增加初始透明度从0.1到0.5
        init_scale=2.0,    # 增加初始尺度从1.0到2.0
        
        # 学习率设置 - 增加学习率
        means_lr=1.6e-3,      # 增加10倍
        scales_lr=5e-2,       # 增加10倍  
        opacities_lr=5e-1,    # 增加10倍
        quats_lr=1e-2,        # 增加10倍
        sh0_lr=2.5e-2,        # 增加10倍
        shN_lr=2.5e-3,        # 增加10倍
        
        # 损失函数设置
        ssim_lambda=0.1,      # 减少SSIM权重，更多依赖L1
        
        # 数据处理
        confidence_threshold=5.0,  # 增加置信度阈值，过滤低质量点
        max_points=50000,          # 限制点数，提高训练效率
        normalize_points=True,
        
        # 正则化 - 添加正则化防止过拟合
        opacity_reg=0.01,
        scale_reg=0.01,
        
        # 日志设置
        tb_every=50,           # 更频繁的日志记录
        tb_save_image=False,   # 暂时关闭图像保存
        
        # 设备
        device="cuda"
    )
    
    print("🚀 开始改进的keyframes 3DGS训练...")
    print("主要改进:")
    print("  ✅ 增加初始透明度: 0.1 → 0.5")
    print("  ✅ 增加初始尺度: 1.0 → 2.0") 
    print("  ✅ 增加学习率: 10倍")
    print("  ✅ 添加正则化")
    print("  ✅ 过滤低质量点")
    print("  ✅ 限制点数提高效率")
    print(f"配置: {cfg}")
    
    trainer = KeyframesTrainer(cfg)
    trainer.train()
    
    print("🎉 改进训练完成!")


if __name__ == "__main__":
    main()
