#!/usr/bin/env python3
"""
修复的keyframes 3DGS训练脚本，解决Gaussian不可见问题
"""

import tyro
from keyframes_trainer import KeyframesTrainer, KeyframesConfig
from gsplat.strategy import DefaultStrategy


def main():
    """修复的训练配置"""
    
    # 禁用修剪策略，避免Gaussian被意外删除
    no_prune_strategy = DefaultStrategy(
        prune_opa=0.0,          # 完全禁用透明度修剪
        grow_grad2d=1e10,       # 设置极高阈值，基本不分裂
        grow_scale3d=1e10,      # 设置极高阈值
        grow_scale2d=1e10,      # 设置极高阈值
        prune_scale3d=1e10,     # 设置极高阈值，基本不修剪
        prune_scale2d=1e10,     # 设置极高阈值
        refine_start_iter=50000, # 延迟到训练结束后才开始修剪
        refine_stop_iter=50001,  # 立即停止修剪
        reset_every=50000,       # 不重置
        refine_every=50000,      # 基本不执行修剪
        verbose=True
    )
    
    # 创建修复的配置
    cfg = KeyframesConfig(
        # 数据设置
        data_path="data/keyframes/24.pkl",
        result_dir="results/keyframes_fixed",
        
        # 训练设置
        max_steps=5000,  # 减少步数，专注于验证修复
        batch_size=1,
        eval_steps=[1000, 3000, 5000],
        save_steps=[1000, 3000, 5000],
        
        # 模型设置 - 关键修复：大幅增加初始尺度
        init_opacity=0.5,      # 高透明度
        init_scale=10.0,       # 大幅增加初始尺度！！！
        
        # 学习率设置 - 适中
        means_lr=1.6e-4,       # 保持原始学习率
        scales_lr=5e-3,        # 保持原始学习率
        opacities_lr=5e-2,     # 保持原始学习率
        quats_lr=1e-3,         # 保持原始学习率
        sh0_lr=2.5e-3,         # 保持原始学习率
        shN_lr=1.25e-4,        # 保持原始学习率
        
        # 损失函数设置
        ssim_lambda=0.2,       # 保持原始设置
        
        # 数据处理
        confidence_threshold=0.0,  # 不过滤，保留所有点
        max_points=None,           # 不限制点数
        normalize_points=True,
        
        # 正则化 - 禁用正则化
        opacity_reg=0.0,
        scale_reg=0.0,
        
        # 策略 - 使用无修剪策略
        strategy=no_prune_strategy,
        
        # 日志设置
        tb_every=100,
        tb_save_image=False,
        
        # 设备
        device="cuda"
    )
    
    print("🔧 开始修复的keyframes 3DGS训练...")
    print("关键修复:")
    print("  🎯 大幅增加初始尺度: 1.0 → 10.0")
    print("  🛡️ 完全禁用修剪策略")
    print("  🔓 移除所有正则化")
    print("  📊 保留所有点云数据")
    print("  ⚡ 使用原始学习率")
    print(f"配置: {cfg}")
    
    trainer = KeyframesTrainer(cfg)
    trainer.train()
    
    print("🎉 修复训练完成!")
    
    # 验证修复效果
    print("\n🔍 验证修复效果...")
    import torch
    try:
        ckpt = torch.load(f"{cfg.result_dir}/ckpts/ckpt_4999.pt", map_location="cuda", weights_only=False)
        splats = ckpt['splats']
        
        opacities_sigmoid = torch.sigmoid(splats["opacities"])
        scales_exp = torch.exp(splats["scales"])
        
        print(f"✅ 最终透明度范围: [{opacities_sigmoid.min():.3f}, {opacities_sigmoid.max():.3f}]")
        print(f"✅ 最终尺度范围: [{scales_exp.min():.3f}, {scales_exp.max():.3f}]")
        print(f"✅ 剩余Gaussian数量: {len(splats['means'])}")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")


if __name__ == "__main__":
    main()
