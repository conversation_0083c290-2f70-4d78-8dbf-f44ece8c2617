#!/usr/bin/env python3
"""
诊断训练问题的脚本
"""

import torch
import numpy as np
from keyframes_dataset import KeyframesDataset, KeyframesParser
from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
from gsplat.rendering import rasterization


def diagnose_initialization():
    """诊断初始化问题"""
    print("=== 诊断初始化问题 ===")
    
    # 加载数据
    dataset = KeyframesDataset(
        data_path="data/keyframes/24.pkl",
        confidence_threshold=2.0,
        max_points=10000,  # 使用较少点数进行诊断
        device="cuda"
    )
    parser = KeyframesParser(dataset)
    
    print(f"数据加载完成: {len(parser.points)} 个点")
    
    # 测试不同的初始化参数
    init_configs = [
        {"init_opacity": 0.1, "init_scale": 1.0, "name": "原始"},
        {"init_opacity": 0.3, "init_scale": 1.5, "name": "适中"},
        {"init_opacity": 0.5, "init_scale": 2.0, "name": "激进"},
        {"init_opacity": 0.8, "init_scale": 0.5, "name": "高透明度小尺度"},
    ]
    
    for config in init_configs:
        print(f"\n--- 测试配置: {config['name']} ---")
        
        try:
            # 创建模型
            splats, _ = create_splats_with_optimizers_keyframes(
                parser,
                init_opacity=config["init_opacity"],
                init_scale=config["init_scale"],
                device="cuda"
            )
            
            # 检查初始参数
            opacities_sigmoid = torch.sigmoid(splats["opacities"])
            scales_exp = torch.exp(splats["scales"])
            
            print(f"  透明度范围: [{opacities_sigmoid.min():.3f}, {opacities_sigmoid.max():.3f}]")
            print(f"  透明度均值: {opacities_sigmoid.mean():.3f}")
            print(f"  透明度>0.1的比例: {(opacities_sigmoid > 0.1).float().mean():.3f}")
            print(f"  尺度范围: [{scales_exp.min():.3f}, {scales_exp.max():.3f}]")
            print(f"  尺度均值: {scales_exp.mean():.3f}")
            
            # 测试渲染
            data_item = dataset[0]
            camtoworld = data_item['camtoworld'][0]
            K = data_item['K'][0]
            
            means = splats["means"]
            quats = splats["quats"]
            scales = scales_exp
            opacities = opacities_sigmoid
            colors = torch.cat([splats["sh0"], splats["shN"]], 1)
            
            render_colors, render_alphas, info = rasterization(
                means=means,
                quats=quats,
                scales=scales,
                opacities=opacities,
                colors=colors,
                viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
                Ks=K.unsqueeze(0),
                width=512,
                height=288,
                sh_degree=3,
            )
            
            print(f"  渲染成功!")
            print(f"  可见Gaussian数量: {(info['radii'] > 0).sum().item()}")
            print(f"  渲染图像范围: [{render_colors.min():.3f}, {render_colors.max():.3f}]")
            
        except Exception as e:
            print(f"  错误: {e}")


def diagnose_pruning():
    """诊断修剪问题"""
    print("\n=== 诊断修剪问题 ===")
    
    # 模拟修剪条件
    dataset = KeyframesDataset(
        data_path="data/keyframes/24.pkl",
        confidence_threshold=2.0,
        max_points=10000,
        device="cuda"
    )
    parser = KeyframesParser(dataset)
    
    splats, _ = create_splats_with_optimizers_keyframes(
        parser,
        init_opacity=0.3,
        init_scale=1.5,
        device="cuda"
    )
    
    # 检查修剪条件
    opacities_sigmoid = torch.sigmoid(splats["opacities"])
    scales_exp = torch.exp(splats["scales"])
    
    # 模拟不同的修剪阈值
    prune_thresholds = [0.001, 0.005, 0.01, 0.05, 0.1]
    
    for threshold in prune_thresholds:
        pruned_mask = opacities_sigmoid < threshold
        remaining = (~pruned_mask).sum().item()
        print(f"  修剪阈值 {threshold}: 剩余 {remaining}/{len(opacities_sigmoid)} 个Gaussian")


def diagnose_data_quality():
    """诊断数据质量"""
    print("\n=== 诊断数据质量 ===")
    
    dataset = KeyframesDataset(
        data_path="data/keyframes/24.pkl",
        confidence_threshold=0.0,  # 不过滤
        device="cuda"
    )
    
    # 检查原始数据
    data_item = dataset[0]
    image = data_item['image'][0]  # [3, H, W]
    
    print(f"图像形状: {image.shape}")
    print(f"图像值范围: [{image.min():.3f}, {image.max():.3f}]")
    print(f"图像均值: {image.mean():.3f}")
    print(f"图像标准差: {image.std():.3f}")
    
    # 检查是否有异常值
    print(f"图像中零值比例: {(image == 0).float().mean():.3f}")
    print(f"图像中一值比例: {(image == 1).float().mean():.3f}")
    
    # 检查点云质量
    points = dataset.points_3d
    print(f"点云数量: {len(points)}")
    print(f"点云范围: X[{points[:, 0].min():.3f}, {points[:, 0].max():.3f}]")
    print(f"点云范围: Y[{points[:, 1].min():.3f}, {points[:, 1].max():.3f}]")
    print(f"点云范围: Z[{points[:, 2].min():.3f}, {points[:, 2].max():.3f}]")
    
    # 检查置信度分布
    if hasattr(dataset, 'data_list') and len(dataset.data_list) > 0:
        confidences = dataset.data_list[0]['confidences']
        print(f"置信度范围: [{confidences.min():.3f}, {confidences.max():.3f}]")
        print(f"置信度均值: {confidences.mean():.3f}")
        print(f"置信度>5.0的比例: {(confidences > 5.0).astype(float).mean():.3f}")


def main():
    """主诊断函数"""
    print("🔍 开始诊断训练问题...")
    
    diagnose_data_quality()
    diagnose_initialization()
    diagnose_pruning()
    
    print("\n🎯 诊断建议:")
    print("1. 如果所有Gaussian都被修剪，可能是初始透明度太低")
    print("2. 如果渲染结果异常，可能是尺度或颜色初始化问题")
    print("3. 如果数据质量有问题，需要调整数据预处理")
    print("4. 建议使用更保守的修剪策略或禁用修剪")


if __name__ == "__main__":
    main()
