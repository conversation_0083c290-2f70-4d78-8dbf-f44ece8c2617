import json
import math
import os
import time
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import imageio
import numpy as np
import torch
import torch.nn.functional as F
import tqdm
import tyro
import yaml
from torch import Tensor
from torch.utils.tensorboard import SummaryWriter
from torchmetrics.image import PeakSignalNoiseRatio, StructuralSimilarityIndexMeasure
from torchmetrics.image.lpip import LearnedPerceptualImagePatchSimilarity

# Import gsplat modules
from gsplat.rendering import rasterization
from gsplat.strategy import DefaultStrategy, MCMCStrategy
from gsplat import export_splats

# Try to import fused_ssim, fallback to torchmetrics if not available
try:
    from fused_ssim import fused_ssim
    FUSED_SSIM_AVAILABLE = True
except ImportError:
    FUSED_SSIM_AVAILABLE = False
    print("fused_ssim not available, using torchmetrics SSIM")

# Import our custom modules
from keyframes_dataset import KeyframesDataset, KeyframesParser
from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
from camera_utils import estimate_camera_intrinsics


@dataclass
class KeyframesConfig:
    """Configuration for keyframes 3DGS training."""
    
    # Data settings
    data_path: str = "data/keyframes/24.pkl"  # Path to pkl file or directory
    result_dir: str = "results/keyframes"  # Directory to save results
    
    # Training settings
    max_steps: int = 10_000  # Number of training steps
    batch_size: int = 1  # Batch size (usually 1 for single image)
    eval_steps: List[int] = field(default_factory=lambda: [3_000, 10_000])
    save_steps: List[int] = field(default_factory=lambda: [3_000, 10_000])
    
    # Model settings
    sh_degree: int = 3  # Spherical harmonics degree
    sh_degree_interval: int = 1000  # Steps to increase SH degree
    init_opacity: float = 0.1  # Initial opacity
    init_scale: float = 1.0  # Initial scale
    
    # Loss settings
    ssim_lambda: float = 0.2  # Weight for SSIM loss
    
    # Optimization settings
    means_lr: float = 1.6e-4  # Learning rate for positions
    scales_lr: float = 5e-3  # Learning rate for scales
    opacities_lr: float = 5e-2  # Learning rate for opacities
    quats_lr: float = 1e-3  # Learning rate for rotations
    sh0_lr: float = 2.5e-3  # Learning rate for SH band 0
    shN_lr: float = 2.5e-3 / 20  # Learning rate for higher SH bands
    
    # Data processing settings
    confidence_threshold: float = 0.0  # Minimum confidence for points
    max_points: Optional[int] = None  # Maximum number of points
    normalize_points: bool = True  # Whether to normalize point cloud
    
    # Camera settings
    camera_fov: float = 60.0  # Field of view for camera intrinsics estimation
    
    # Strategy settings
    strategy: Union[DefaultStrategy, MCMCStrategy] = field(
        default_factory=lambda: DefaultStrategy(verbose=True)
    )
    
    # Rendering settings
    near_plane: float = 0.01
    far_plane: float = 1e10
    packed: bool = False
    antialiased: bool = False
    
    # Regularization
    opacity_reg: float = 0.0
    scale_reg: float = 0.0
    
    # Logging
    tb_every: int = 100  # Tensorboard logging frequency
    tb_save_image: bool = False
    
    # Device settings
    device: str = "cuda"
    
    # Viewer settings
    disable_viewer: bool = True  # Disable viewer for now
    port: int = 8080


class KeyframesTrainer:
    """Trainer for 3D Gaussian Splatting with keyframes data."""
    
    def __init__(self, cfg: KeyframesConfig):
        self.cfg = cfg
        self.device = cfg.device
        
        # Setup output directories
        os.makedirs(cfg.result_dir, exist_ok=True)
        self.ckpt_dir = f"{cfg.result_dir}/ckpts"
        os.makedirs(self.ckpt_dir, exist_ok=True)
        self.stats_dir = f"{cfg.result_dir}/stats"
        os.makedirs(self.stats_dir, exist_ok=True)
        self.render_dir = f"{cfg.result_dir}/renders"
        os.makedirs(self.render_dir, exist_ok=True)
        
        # Tensorboard
        self.writer = SummaryWriter(log_dir=f"{cfg.result_dir}/tb")
        
        # Load dataset
        print(f"Loading dataset from: {cfg.data_path}")
        self.dataset = KeyframesDataset(
            data_path=cfg.data_path,
            confidence_threshold=cfg.confidence_threshold,
            max_points=cfg.max_points,
            normalize_points=cfg.normalize_points,
            device=self.device
        )
        
        # Create parser for compatibility
        self.parser = KeyframesParser(self.dataset)
        self.scene_scale = self.parser.scene_scale
        
        print(f"Dataset loaded: {len(self.dataset)} frames")
        print(f"Scene scale: {self.scene_scale:.3f}")
        
        # Initialize model
        self.splats, self.optimizers = create_splats_with_optimizers_keyframes(
            self.parser,
            init_opacity=cfg.init_opacity,
            init_scale=cfg.init_scale,
            means_lr=cfg.means_lr,
            scales_lr=cfg.scales_lr,
            opacities_lr=cfg.opacities_lr,
            quats_lr=cfg.quats_lr,
            sh0_lr=cfg.sh0_lr,
            shN_lr=cfg.shN_lr,
            scene_scale=self.scene_scale,
            sh_degree=cfg.sh_degree,
            device=self.device,
        )
        
        print(f"Model initialized. Number of GS: {len(self.splats['means'])}")
        
        # Initialize strategy
        self.cfg.strategy.check_sanity(self.splats, self.optimizers)
        if isinstance(self.cfg.strategy, DefaultStrategy):
            self.strategy_state = self.cfg.strategy.initialize_state(
                scene_scale=self.scene_scale
            )
        elif isinstance(self.cfg.strategy, MCMCStrategy):
            self.strategy_state = self.cfg.strategy.initialize_state()
        
        # Initialize metrics
        self.ssim = StructuralSimilarityIndexMeasure(data_range=1.0).to(self.device)
        self.psnr = PeakSignalNoiseRatio(data_range=1.0).to(self.device)
        self.lpips = LearnedPerceptualImagePatchSimilarity(
            net_type="alex", normalize=True
        ).to(self.device)
    
    def rasterize_splats(
        self,
        camtoworlds: Tensor,
        Ks: Tensor,
        width: int,
        height: int,
        **kwargs,
    ) -> Tuple[Tensor, Tensor, Dict]:
        """Rasterize 3D Gaussian splats."""
        means = self.splats["means"]  # [N, 3]
        quats = self.splats["quats"]  # [N, 4]
        scales = torch.exp(self.splats["scales"])  # [N, 3]
        opacities = torch.sigmoid(self.splats["opacities"])  # [N,]
        colors = torch.cat([self.splats["sh0"], self.splats["shN"]], 1)  # [N, K, 3]
        
        render_colors, render_alphas, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworlds),  # [C, 4, 4]
            Ks=Ks,  # [C, 3, 3]
            width=width,
            height=height,
            packed=self.cfg.packed,
            absgrad=(
                self.cfg.strategy.absgrad
                if isinstance(self.cfg.strategy, DefaultStrategy)
                else False
            ),
            rasterize_mode="antialiased" if self.cfg.antialiased else "classic",
            sh_degree=kwargs.get("sh_degree", self.cfg.sh_degree),  # Add sh_degree
            **{k: v for k, v in kwargs.items() if k != "sh_degree"},  # Remove sh_degree from kwargs
        )
        
        return render_colors, render_alphas, info
    
    def train(self):
        """Main training loop."""
        cfg = self.cfg
        
        # Save config
        with open(f"{cfg.result_dir}/cfg.yml", "w") as f:
            yaml.dump(vars(cfg), f)
        
        # Setup data loader
        dataloader = torch.utils.data.DataLoader(
            self.dataset,
            batch_size=cfg.batch_size,
            shuffle=True,
            num_workers=0,  # Use 0 for debugging
            pin_memory=False,  # Disable pin_memory since data is already on GPU
        )
        
        # Setup learning rate scheduler
        scheduler = torch.optim.lr_scheduler.ExponentialLR(
            self.optimizers["means"], gamma=0.01 ** (1.0 / cfg.max_steps)
        )
        
        # Training loop
        global_tic = time.time()
        pbar = tqdm.tqdm(range(cfg.max_steps))
        dataloader_iter = iter(dataloader)
        
        for step in pbar:
            try:
                data = next(dataloader_iter)
            except StopIteration:
                dataloader_iter = iter(dataloader)
                data = next(dataloader_iter)
            
            # Extract data
            camtoworlds = data["camtoworld"]  # [batch, 1, 4, 4] or [batch, 4, 4]
            Ks = data["K"]  # [batch, 1, 3, 3] or [batch, 3, 3]
            images = data["image"]  # [batch, 1, 3, H, W] or [batch, 3, H, W]

            # Handle potential extra dimensions from DataLoader
            if camtoworlds.dim() == 4:  # [batch, 1, 4, 4]
                camtoworlds = camtoworlds.squeeze(1)  # [batch, 4, 4]
            if Ks.dim() == 4:  # [batch, 1, 3, 3]
                Ks = Ks.squeeze(1)  # [batch, 3, 3]
            if images.dim() == 5:  # [batch, 1, 3, H, W]
                images = images.squeeze(1)  # [batch, 3, H, W]

            # Convert image format for loss computation
            pixels = images.permute(0, 2, 3, 1)  # [batch, H, W, 3]
            height, width = pixels.shape[1:3]
            
            # SH degree schedule
            sh_degree_to_use = min(step // cfg.sh_degree_interval, cfg.sh_degree)
            
            # Forward pass
            renders, alphas, info = self.rasterize_splats(
                camtoworlds=camtoworlds,
                Ks=Ks,
                width=width,
                height=height,
                sh_degree=sh_degree_to_use,
                near_plane=cfg.near_plane,
                far_plane=cfg.far_plane,
            )
            
            colors = renders  # [1, H, W, 3]
            
            # Strategy pre-backward step
            self.cfg.strategy.step_pre_backward(
                params=self.splats,
                optimizers=self.optimizers,
                state=self.strategy_state,
                step=step,
                info=info,
            )
            
            # Compute loss
            l1loss = F.l1_loss(colors, pixels)

            # Compute SSIM loss
            if FUSED_SSIM_AVAILABLE:
                ssimloss = 1.0 - fused_ssim(
                    colors.permute(0, 3, 1, 2), pixels.permute(0, 3, 1, 2), padding="valid"
                )
            else:
                ssimloss = 1.0 - self.ssim(
                    colors.permute(0, 3, 1, 2), pixels.permute(0, 3, 1, 2)
                )

            loss = l1loss * (1.0 - cfg.ssim_lambda) + ssimloss * cfg.ssim_lambda
            
            # Regularization
            if cfg.opacity_reg > 0.0:
                loss += cfg.opacity_reg * torch.sigmoid(self.splats["opacities"]).mean()
            if cfg.scale_reg > 0.0:
                loss += cfg.scale_reg * torch.exp(self.splats["scales"]).mean()
            
            loss.backward()
            
            # Update progress bar
            desc = f"loss={loss.item():.3f}| sh degree={sh_degree_to_use}| "
            pbar.set_description(desc)
            
            # Logging
            if step % cfg.tb_every == 0:
                self.writer.add_scalar("train/loss", loss.item(), step)
                self.writer.add_scalar("train/l1loss", l1loss.item(), step)
                self.writer.add_scalar("train/ssimloss", ssimloss.item(), step)
                self.writer.add_scalar("train/num_GS", len(self.splats["means"]), step)
                
                if cfg.tb_save_image:
                    canvas = torch.cat([pixels, colors], dim=2).squeeze(0).detach().cpu().numpy()
                    self.writer.add_image("train/render", canvas, step)
                
                self.writer.flush()
            
            # Save checkpoint
            if step in [i - 1 for i in cfg.save_steps] or step == cfg.max_steps - 1:
                self.save_checkpoint(step)
            
            # Evaluation
            if step in [i - 1 for i in cfg.eval_steps]:
                self.eval(step)
            
            # Optimize
            for optimizer in self.optimizers.values():
                optimizer.step()
                optimizer.zero_grad(set_to_none=True)
            scheduler.step()
            
            # Strategy post-backward step
            if isinstance(self.cfg.strategy, DefaultStrategy):
                self.cfg.strategy.step_post_backward(
                    params=self.splats,
                    optimizers=self.optimizers,
                    state=self.strategy_state,
                    step=step,
                    info=info,
                    packed=cfg.packed,
                )
            elif isinstance(self.cfg.strategy, MCMCStrategy):
                self.cfg.strategy.step_post_backward(
                    params=self.splats,
                    optimizers=self.optimizers,
                    state=self.strategy_state,
                    step=step,
                    info=info,
                    lr=scheduler.get_last_lr()[0],
                )
        
        print(f"Training completed in {time.time() - global_tic:.2f} seconds")
    
    def save_checkpoint(self, step: int):
        """Save model checkpoint."""
        data = {
            "step": step,
            "splats": self.splats.state_dict(),
            "scene_scale": self.scene_scale,
        }
        torch.save(data, f"{self.ckpt_dir}/ckpt_{step}.pt")
        print(f"Checkpoint saved at step {step}")
    
    @torch.no_grad()
    def eval(self, step: int):
        """Evaluate model."""
        print(f"Evaluating at step {step}...")
        
        # Use the same data for evaluation (could be extended to validation set)
        dataloader = torch.utils.data.DataLoader(
            self.dataset, batch_size=1, shuffle=False
        )
        
        metrics = defaultdict(list)
        for i, data in enumerate(dataloader):
            camtoworlds = data["camtoworld"]
            Ks = data["K"]
            images = data["image"]

            # Handle potential extra dimensions from DataLoader
            if camtoworlds.dim() == 4:  # [batch, 1, 4, 4]
                camtoworlds = camtoworlds.squeeze(1)  # [batch, 4, 4]
            if Ks.dim() == 4:  # [batch, 1, 3, 3]
                Ks = Ks.squeeze(1)  # [batch, 3, 3]
            if images.dim() == 5:  # [batch, 1, 3, H, W]
                images = images.squeeze(1)  # [batch, 3, H, W]

            pixels = images.permute(0, 2, 3, 1)
            height, width = pixels.shape[1:3]
            
            # Render
            colors, _, _ = self.rasterize_splats(
                camtoworlds=camtoworlds,
                Ks=Ks,
                width=width,
                height=height,
                sh_degree=self.cfg.sh_degree,
                near_plane=self.cfg.near_plane,
                far_plane=self.cfg.far_plane,
            )
            
            colors = torch.clamp(colors, 0.0, 1.0)
            
            # Save rendered image
            canvas = torch.cat([pixels, colors], dim=2).squeeze(0).cpu().numpy()
            canvas = (canvas * 255).astype(np.uint8)
            imageio.imwrite(
                f"{self.render_dir}/eval_step{step}_{i:04d}.png", canvas
            )
            
            # Compute metrics
            pixels_p = pixels.permute(0, 3, 1, 2)
            colors_p = colors.permute(0, 3, 1, 2)
            metrics["psnr"].append(self.psnr(colors_p, pixels_p))
            metrics["ssim"].append(self.ssim(colors_p, pixels_p))
            metrics["lpips"].append(self.lpips(colors_p, pixels_p))
        
        # Aggregate metrics
        stats = {k: torch.stack(v).mean().item() for k, v in metrics.items()}
        stats["num_GS"] = len(self.splats["means"])
        
        print(f"PSNR: {stats['psnr']:.3f}, SSIM: {stats['ssim']:.4f}, "
              f"LPIPS: {stats['lpips']:.3f}, GS: {stats['num_GS']}")
        
        # Save stats
        with open(f"{self.stats_dir}/eval_step{step:04d}.json", "w") as f:
            json.dump(stats, f)
        
        # Log to tensorboard
        for k, v in stats.items():
            self.writer.add_scalar(f"eval/{k}", v, step)
        self.writer.flush()


def main():
    """Main function."""
    cfg = tyro.cli(KeyframesConfig)
    
    print("Starting keyframes 3DGS training...")
    print(f"Config: {cfg}")
    
    trainer = KeyframesTrainer(cfg)
    trainer.train()
    
    print("Training completed!")


if __name__ == "__main__":
    main()
